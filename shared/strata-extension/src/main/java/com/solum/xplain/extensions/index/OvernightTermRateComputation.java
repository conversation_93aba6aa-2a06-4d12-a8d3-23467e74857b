package com.solum.xplain.extensions.index;

import com.google.common.collect.ImmutableSet;
import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.index.Index;
import com.opengamma.strata.basics.index.OvernightIndex;
import com.opengamma.strata.product.rate.OvernightCompoundedRateComputation;
import com.opengamma.strata.product.rate.RateComputation;
import com.opengamma.strata.product.swap.OvernightAccrualMethod;
import java.io.Serializable;
import java.time.LocalDate;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.jspecify.annotations.NullMarked;

/**
 * A rate computation for overnight term rates that includes a custom termFirstFixingDate.
 *
 * <p>This class wraps an OvernightCompoundedRateComputation and adds the termFirstFixingDate that
 * is needed by the pricing logic for proper term rate calculations.
 */
@NullMarked
@Getter
@EqualsAndHashCode
public final class OvernightTermRateComputation implements RateComputation, Serializable {

  private final OvernightCompoundedRateComputation delegate;
  private final LocalDate termFirstFixingDate;

  /**
   * @param delegate the underlying overnight compounded rate computation
   * @param termFirstFixingDate the first fixing date for term rate calculation
   */
  private OvernightTermRateComputation(
      OvernightCompoundedRateComputation delegate, LocalDate termFirstFixingDate) {
    this.delegate = delegate;
    this.termFirstFixingDate = termFirstFixingDate;
  }

  /**
   * Creates an OvernightTermRateComputation with the specified parameters.
   *
   * @param index the overnight index
   * @param startDate the start date of the accrual period
   * @param endDate the end date of the accrual period
   * @param rateCutOffDays the rate cut-off days
   * @param accrualMethod the accrual method
   * @param termFirstFixingDate the first fixing date for term rate calculation
   * @param refData the reference data
   * @return the overnight term rate computation
   */
  public static OvernightTermRateComputation of(
      OvernightIndex index,
      LocalDate startDate,
      LocalDate endDate,
      int rateCutOffDays,
      OvernightAccrualMethod accrualMethod,
      LocalDate termFirstFixingDate,
      ReferenceData refData) {

    OvernightCompoundedRateComputation delegate =
        OvernightCompoundedRateComputation.of(index, startDate, endDate, rateCutOffDays, refData);

    return new OvernightTermRateComputation(delegate, termFirstFixingDate);
  }

  /**
   * Creates an OvernightTermRateComputation from an existing OvernightCompoundedRateComputation
   * with termFirstFixingDate.
   *
   * @param delegate the underlying overnight compounded rate computation
   * @param termFirstFixingDate the first fixing date for term rate calculation
   * @return the overnight term rate computation
   */
  public static OvernightTermRateComputation of(
      OvernightCompoundedRateComputation delegate, LocalDate termFirstFixingDate) {
    return new OvernightTermRateComputation(delegate, termFirstFixingDate);
  }

  // Overnight term index extends overnight index
  public OvernightIndex getIndex() {
    return delegate.getIndex();
  }

  public LocalDate getStartDate() {
    return delegate.getStartDate();
  }

  public LocalDate getEndDate() {
    return delegate.getEndDate();
  }

  public int getRateCutOffDays() {
    return delegate.getRateCutOffDays();
  }

  @Override
  public void collectIndices(ImmutableSet.Builder<Index> builder) {
    delegate.collectIndices(builder);
  }

  @Override
  public String toString() {
    return "OvernightTermRateComputation{"
        + "termFirstFixingDate="
        + termFirstFixingDate
        + ", delegate="
        + delegate
        + '}';
  }
}
