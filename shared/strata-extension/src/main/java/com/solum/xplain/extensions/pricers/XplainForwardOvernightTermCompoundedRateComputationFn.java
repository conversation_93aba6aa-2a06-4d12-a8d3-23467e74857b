/*
 * Copyright (C) 2015 - present by OpenGamma Inc. and the OpenGamma group of companies
 *
 * Please see distribution for license.
 */
package com.solum.xplain.extensions.pricers;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.market.explain.ExplainKey;
import com.opengamma.strata.market.explain.ExplainMapBuilder;
import com.opengamma.strata.market.sensitivity.PointSensitivityBuilder;
import com.opengamma.strata.pricer.rate.RateComputationFn;
import com.opengamma.strata.pricer.rate.RatesProvider;
import com.solum.xplain.extensions.index.OvernightTermRateComputation;
import java.time.LocalDate;
import lombok.RequiredArgsConstructor;
import org.jspecify.annotations.NullMarked;

/**
 * Rate computation implementation for {@link OvernightTermRateComputation}.
 *
 * <p>This implementation delegates to the existing {@link
 * XplainForwardOvernightCompoundedRateComputation} by converting the term computation to a standard
 * overnight compounded computation.
 */
@NullMarked
@RequiredArgsConstructor
public class XplainForwardOvernightTermCompoundedRateComputationFn
    implements RateComputationFn<OvernightTermRateComputation> {

  private final XplainForwardOvernightCompoundedRateComputation delegate;

  public XplainForwardOvernightTermCompoundedRateComputationFn(ReferenceData referenceData) {
    this.delegate = new XplainForwardOvernightCompoundedRateComputation(referenceData);
  }

  @Override
  public double rate(
      OvernightTermRateComputation computation,
      LocalDate startDate,
      LocalDate endDate,
      RatesProvider provider) {

    // Delegate to the existing XplainOvernightTermRateComputationFn logic
    return delegate.rate(
        computation.getDelegate(),
        startDate,
        endDate,
        provider,
        computation.getTermFirstFixingDate());
  }

  @Override
  public PointSensitivityBuilder rateSensitivity(
      OvernightTermRateComputation computation,
      LocalDate startDate,
      LocalDate endDate,
      RatesProvider provider) {

    var rates = provider.overnightIndexRates(computation.getDelegate().getIndex());

    return delegate.rateSensitivity(
        computation.getDelegate(),
        startDate,
        endDate,
        rates,
        delegate.getReferenceData(),
        computation.getTermFirstFixingDate());
  }

  @Override
  public double explainRate(
      OvernightTermRateComputation computation,
      LocalDate startDate,
      LocalDate endDate,
      RatesProvider provider,
      ExplainMapBuilder builder) {

    double rate = rate(computation, startDate, endDate, provider);
    builder.put(ExplainKey.COMBINED_RATE, rate);
    return rate;
  }

  public ReferenceData getReferenceData() {
    return delegate.getReferenceData();
  }
}
