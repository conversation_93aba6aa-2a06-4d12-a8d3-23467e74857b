/*
 * Copyright (C) 2015 - present by OpenGamma Inc. and the OpenGamma group of companies
 *
 * Please see distribution for license.
 */
package com.solum.xplain.extensions.pricers;

import static com.solum.xplain.extensions.utils.OvernightAccrualsFixingUtils.checkedFixing;
import static com.solum.xplain.extensions.utils.OvernightAccrualsFixingUtils.fixingPublishedOnValuationDate;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.date.DayCount;
import com.opengamma.strata.basics.date.HolidayCalendar;
import com.opengamma.strata.basics.index.OvernightIndexObservation;
import com.opengamma.strata.collect.timeseries.LocalDateDoubleTimeSeries;
import com.opengamma.strata.collect.tuple.ObjDoublePair;
import com.opengamma.strata.market.explain.ExplainKey;
import com.opengamma.strata.market.explain.ExplainMapBuilder;
import com.opengamma.strata.market.sensitivity.PointSensitivityBuilder;
import com.opengamma.strata.pricer.rate.OvernightIndexRates;
import com.opengamma.strata.pricer.rate.RateComputationFn;
import com.opengamma.strata.pricer.rate.RatesProvider;
import com.opengamma.strata.product.rate.OvernightCompoundedRateComputation;
import com.solum.xplain.extensions.calendar.ValuationDateReferenceData;
import com.solum.xplain.extensions.index.OvernightTermIndex;
import com.solum.xplain.extensions.utils.OvernightIndexRatesConverter;
import java.time.LocalDate;
import java.util.List;
import java.util.OptionalDouble;
import lombok.RequiredArgsConstructor;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;

/**
 * Rate computation implementation for a rate based on a single overnight index that is compounded.
 *
 * <p>Rates that are already fixed are retrieved from the time series of the {@link RatesProvider}.
 * Rates that are in the future and not in the cut-off period are computed as unique forward rate in
 * the full future period. Rates that are in the cut-off period (already fixed or forward) are
 * compounded.
 */
@NullMarked
@RequiredArgsConstructor
public class XplainForwardOvernightCompoundedRateComputation
    implements RateComputationFn<OvernightCompoundedRateComputation> {
  private final ReferenceData referenceData;

  // -------------------------------------------------------------------------
  @Override
  public double rate(
      OvernightCompoundedRateComputation computation,
      LocalDate startDate,
      LocalDate endDate,
      RatesProvider provider) {
    return rate(computation, startDate, endDate, provider, null);
  }

  public double rate(
      OvernightCompoundedRateComputation computation,
      LocalDate startDate,
      LocalDate endDate,
      RatesProvider provider,
      @Nullable LocalDate termFirstFixingDate) {

    OvernightIndexRates rates = provider.overnightIndexRates(computation.getIndex());
    boolean isOvernightTerm = isOvernightTermIndex(computation.getIndex());
    LocalDate overnightTermFirstFixingDate =
        determineTermFirstFixingDate(computation, startDate, termFirstFixingDate);

    ObservationDetails details =
        new ObservationDetails(
            computation,
            startDate,
            endDate,
            rates,
            referenceData,
            isOvernightTerm,
            overnightTermFirstFixingDate);
    return details.calculateRate();
  }

  @Override
  public PointSensitivityBuilder rateSensitivity(
      OvernightCompoundedRateComputation computation,
      LocalDate startDate,
      LocalDate endDate,
      RatesProvider provider) {
    OvernightIndexRates rates = provider.overnightIndexRates(computation.getIndex());
    return rateSensitivity(computation, startDate, endDate, rates, referenceData, null);
  }

  public PointSensitivityBuilder rateSensitivity(
      OvernightCompoundedRateComputation computation,
      LocalDate startDate,
      LocalDate endDate,
      OvernightIndexRates rates,
      ReferenceData referenceData,
      @Nullable LocalDate termFirstFixingDate) {

    boolean isOvernightTerm =
        isOvernightTermIndex(computation.getIndex()) || termFirstFixingDate != null;
    LocalDate effectiveTermFirstFixingDate =
        determineTermFirstFixingDate(computation, startDate, termFirstFixingDate);

    ObservationDetails details =
        new ObservationDetails(
            computation,
            startDate,
            endDate,
            rates,
            referenceData,
            isOvernightTerm,
            effectiveTermFirstFixingDate);

    return details.calculateRateSensitivity();
  }

  @Override
  public double explainRate(
      OvernightCompoundedRateComputation computation,
      LocalDate startDate,
      LocalDate endDate,
      RatesProvider provider,
      ExplainMapBuilder builder) {

    double rate = rate(computation, startDate, endDate, provider);
    builder.put(ExplainKey.COMBINED_RATE, rate);
    return rate;
  }

  public ReferenceData getReferenceData() {
    return referenceData;
  }

  /** Determines if the given index is an overnight term index. */
  private boolean isOvernightTermIndex(com.opengamma.strata.basics.index.OvernightIndex index) {
    return index instanceof OvernightTermIndex;
  }

  /**
   * Determines the effective term first fixing date based on the computation and provided
   * parameters.
   */
  private LocalDate determineTermFirstFixingDate(
      OvernightCompoundedRateComputation computation,
      LocalDate startDate,
      @Nullable LocalDate termFirstFixingDate) {

    if (termFirstFixingDate != null) {
      return termFirstFixingDate;
    }else if (computation.getIndex() instanceof OvernightTermIndex termIndex) {
      return termIndex.getFixingDateOffset().adjust(startDate, referenceData);
    }else{
      return startDate;
    }
  }

  // -------------------------------------------------------------------------
  // Enum for overnight term rate calculation scenarios
  private enum OvernightTermRateScenario {
    FORWARD_LOOKING, // Valuation date before fixing date
    HISTORICAL_FIXING, // Valuation date after fixing date
    FIXING_DATE_WITH_FIXING, // Valuation date equals fixing date and fixing is available
    FIXING_DATE_NO_FIXING // Valuation date equals fixing date but no fixing available
  }

  // -------------------------------------------------------------------------
  // Internal class. Observation details stored in a separate class to clarify the construction.
  private static final class ObservationDetails {

    private final OvernightCompoundedRateComputation computation;
    private final OvernightIndexRates rates;
    private final LocalDateDoubleTimeSeries indexFixingDateSeries;
    private final DayCount dayCount;
    private final int cutoffOffset;
    private final LocalDate firstFixing; // The date of the first fixing
    private final LocalDate lastFixingNonCutoff; // The last fixing not in the cutoff period.
    private final double[]
        accrualFactorCutoff; // Accrual factors for the sub-periods using the cutoff rate.
    private final boolean isOvernightTerm;
    @Nullable private LocalDate nextFixing; // Running variable through the different methods: next fixing date to be
    // analyzed
    private final double accrualFactorTotal; // Total accrual factor
    private final HolidayCalendar
        unadjustedFixingCalendar; // fixing calendar before enforcing additional bus days
    private final LocalDate unadjustedStartDate; // start date of accrual period
    private final LocalDate
        termFirstFixingDate; // fixing date that is used for the first period pulled at the trade

    // level for term overnight

    private ObservationDetails(
        OvernightCompoundedRateComputation computation,
        LocalDate startDate,
        LocalDate endDate,
        OvernightIndexRates rates,
        ReferenceData referenceData,
        boolean isOvernightTerm,
        LocalDate termFirstFixingDate) {
      rates = OvernightIndexRatesConverter.overnightRates(rates, computation.getFixingCalendar());
      this.unadjustedFixingCalendar = computation.getFixingCalendar();
      this.unadjustedStartDate = startDate;
      var enforcedBusinessDays = List.of(rates.getValuationDate(), startDate, endDate);
      var overrideComputation =
          OvernightCompoundedRateComputation.of(
              computation.getIndex(),
              computation.getStartDate(),
              computation.getEndDate(),
              computation.getRateCutOffDays(),
              ValuationDateReferenceData.wrap(referenceData, enforcedBusinessDays));

      this.computation = overrideComputation;
      this.rates = rates;
      this.indexFixingDateSeries = rates.getFixings();
      this.dayCount = overrideComputation.getIndex().getDayCount();

      this.termFirstFixingDate = termFirstFixingDate;
      // Details of the cutoff period
      this.firstFixing = startDate;
      // The date after the last fixing
      // The date of the last fixing
      LocalDate lastFixing = overrideComputation.getFixingCalendar().previous(endDate);
      this.cutoffOffset = Math.max(overrideComputation.getRateCutOffDays(), 1);
      this.accrualFactorCutoff = new double[cutoffOffset - 1];
      LocalDate currentFixing = lastFixing;
      for (int i = 0; i < cutoffOffset - 1; i++) {
        currentFixing = overrideComputation.getFixingCalendar().previous(currentFixing);
        LocalDate effectiveDate = overrideComputation.calculateEffectiveFromFixing(currentFixing);
        LocalDate maturityDate = overrideComputation.calculateMaturityFromEffective(effectiveDate);
        accrualFactorCutoff[i] = dayCount.yearFraction(effectiveDate, maturityDate);
      }
      this.lastFixingNonCutoff = currentFixing;
      this.accrualFactorTotal = dayCount.yearFraction(startDate, endDate);
      this.isOvernightTerm = isOvernightTerm;
    }

    // Composition - publication strictly before valuation date: try accessing fixing time-series
    private double pastCompositionFactor() {
      double compositionFactor = 1.0d;
      LocalDate currentFixing = firstFixing;
      LocalDate currentPublication = computation.calculatePublicationFromFixing(currentFixing);
      while ((currentFixing.isBefore(lastFixingNonCutoff))
          && // fixing in the non-cutoff period
          rates.getValuationDate().isAfter(currentPublication)) { // publication before valuation
        LocalDate effectiveDate = computation.calculateEffectiveFromFixing(currentFixing);
        LocalDate maturityDate = computation.calculateMaturityFromEffective(effectiveDate);
        double accrualFactor = dayCount.yearFraction(effectiveDate, maturityDate);
        double rate =
            checkedFixing(
                currentFixing,
                indexFixingDateSeries,
                computation.getIndex(),
                unadjustedStartDate,
                unadjustedFixingCalendar);

        compositionFactor *= 1.0d + accrualFactor * rate;
        currentFixing = computation.getFixingCalendar().next(currentFixing);
        currentPublication = computation.calculatePublicationFromFixing(currentFixing);
      }
      if (currentFixing.equals(lastFixingNonCutoff)
          &&
          // fixing is on the last non-cutoff date, cutoff period known
          rates.getValuationDate().isAfter(currentPublication)) { // publication before valuation
        double rate =
            checkedFixing(
                currentFixing,
                indexFixingDateSeries,
                computation.getIndex(),
                unadjustedStartDate,
                unadjustedFixingCalendar);
        LocalDate effectiveDate = computation.calculateEffectiveFromFixing(currentFixing);
        LocalDate maturityDate = computation.calculateMaturityFromEffective(effectiveDate);
        double accrualFactor = dayCount.yearFraction(effectiveDate, maturityDate);
        compositionFactor *= 1.0d + accrualFactor * rate;
        for (int i = 0; i < cutoffOffset - 1; i++) {
          compositionFactor *= 1.0d + accrualFactorCutoff[i] * rate;
        }
        currentFixing = computation.getFixingCalendar().next(currentFixing);
      }
      nextFixing = currentFixing;
      return compositionFactor;
    }

    // Composition - publication on valuation date: Check if a fixing is available on current date
    private double valuationCompositionFactor() {
      LocalDate currentFixing = nextFixing;
      LocalDate currentPublication = computation.calculatePublicationFromFixing(currentFixing);

      if (rates.getValuationDate().equals(currentPublication)
          && !(currentFixing.isAfter(
              lastFixingNonCutoff))) { // If currentFixing > lastFixingNonCutoff, everything fixed

        OptionalDouble fixedRate =
            fixingPublishedOnValuationDate(
                currentFixing,
                currentPublication,
                indexFixingDateSeries,
                unadjustedStartDate,
                unadjustedFixingCalendar);
        if (fixedRate.isPresent()) {
          nextFixing = computation.getFixingCalendar().next(nextFixing);
          LocalDate effectiveDate = computation.calculateEffectiveFromFixing(currentFixing);
          LocalDate maturityDate = computation.calculateMaturityFromEffective(effectiveDate);
          double accrualFactor = dayCount.yearFraction(effectiveDate, maturityDate);
          if (currentFixing.isBefore(lastFixingNonCutoff)) {
            return 1.0d + accrualFactor * fixedRate.getAsDouble();
          }
          double compositionFactor = 1.0d + accrualFactor * fixedRate.getAsDouble();
          for (int i = 0; i < cutoffOffset - 1; i++) {
            compositionFactor *= 1.0d + accrualFactorCutoff[i] * fixedRate.getAsDouble();
          }
          return compositionFactor;
        }
      }
      return 1.0d;
    }

    // Composition - forward part in non-cutoff period; past/valuation date case dealt with in
    // previous methods
    private double compositionFactorNonCutoff() {
      if (!nextFixing.isAfter(lastFixingNonCutoff)) {
        OvernightIndexObservation obs = computation.observeOn(nextFixing);
        LocalDate startDate = obs.getEffectiveDate();
        LocalDate endDate = computation.calculateMaturityFromFixing(lastFixingNonCutoff);
        double accrualFactor = dayCount.yearFraction(startDate, endDate);
        double rate = rates.periodRate(obs, endDate);
        return 1.0d + accrualFactor * rate;
      }
      return 1.0d;
    }

    // Composition - forward part in the cutoff period; past/valuation date case dealt with in
    // previous methods
    private double compositionFactorCutoff() {
      if (!nextFixing.isAfter(lastFixingNonCutoff)) {
        OvernightIndexObservation obs = computation.observeOn(lastFixingNonCutoff);
        double rate = rates.rate(obs);
        double compositionFactor = 1.0d;
        for (int i = 0; i < cutoffOffset - 1; i++) {
          compositionFactor *= 1.0d + accrualFactorCutoff[i] * rate;
        }
        return compositionFactor;
      }
      return 1.0d;
    }

    /**
     * Calculate the total rate using either overnight compounding or term rate logic. For overnight
     * term rates: - If valuation date is before fixing date: use forward-looking overnight rate -
     * If valuation date is after fixing date: use historical term fixing - If valuation date equals
     * fixing date: use fixing if available, otherwise fallback to overnight rate For regular
     * overnight rates: use standard overnight compounding logic
     */
    double calculateRate() {
      if (isOvernightTerm) {
        return calculateOvernightTermRate();
      }
      return calculateOvernightRate();
    }

    /**
     * Calculate rate for overnight term indices based on valuation date relative to fixing date.
     */
    private double calculateOvernightTermRate() {
      LocalDate fixingDate = termFirstFixingDate;
      LocalDate valuationDate = rates.getValuationDate();

      OvernightTermRateScenario scenario =
          determineOvernightTermScenario(valuationDate, fixingDate);

      return switch (scenario) {
        case FORWARD_LOOKING, FIXING_DATE_NO_FIXING -> calculateOvernightRate();
        case HISTORICAL_FIXING, FIXING_DATE_WITH_FIXING -> calculateHistoricalTermRate(fixingDate);
      };
    }

    /** Determine the scenario for overnight term rate calculation. */
    private OvernightTermRateScenario determineOvernightTermScenario(
        LocalDate valuationDate, LocalDate fixingDate) {
      if (valuationDate.isBefore(fixingDate)) {
        return OvernightTermRateScenario.FORWARD_LOOKING;
      } else if (valuationDate.isAfter(fixingDate)) {
        return OvernightTermRateScenario.HISTORICAL_FIXING;
      } else {
        // valuationDate.equals(fixingDate)
        OptionalDouble fixedRate = indexFixingDateSeries.get(fixingDate);
        return fixedRate.isPresent()
            ? OvernightTermRateScenario.FIXING_DATE_WITH_FIXING
            : OvernightTermRateScenario.FIXING_DATE_NO_FIXING;
      }
    }

    /** Calculate rate using historical term fixing. */
    private double calculateHistoricalTermRate(LocalDate fixingDate) {
      return (pastFixedCompositionFactor(fixingDate) - 1.0) / accrualFactorTotal;
    }

    private double pastFixedCompositionFactor(LocalDate fixingDate) {
      OptionalDouble fixedRate = indexFixingDateSeries.get(fixingDate);
      if (fixedRate.isEmpty()) {
        return 1.0; // No fixing available, no rate to apply
      }

      LocalDate effectiveDate = computation.calculateEffectiveFromFixing(firstFixing);
      LocalDate maturityDate = computation.observeOn(lastFixingNonCutoff).getMaturityDate();
      double accrualFactor = dayCount.yearFraction(effectiveDate, maturityDate);

      return 1.0 + accrualFactor * fixedRate.getAsDouble();
    }

    /**
     * Calculates the overnight rate. Based on the logic in OvernightCompoundedRateComputationFn.
     * Looks at the past, valuation date, and forward period. Then compounds the rates.
     *
     * @return overnight rate
     */
    private double calculateOvernightRate() {
      return (pastCompositionFactor()
                  * valuationCompositionFactor()
                  * compositionFactorNonCutoff()
                  * compositionFactorCutoff()
              - 1.0d)
          / accrualFactorTotal;
    }

    /**
     * Calculate rate sensitivity using either overnight compounding or term rate logic. For
     * overnight term rates: - Forward-looking scenarios: return overnight rate sensitivity -
     * Historical fixing scenarios: return no sensitivity (fixed rate) For regular overnight rates:
     * use standard overnight sensitivity logic
     */
    PointSensitivityBuilder calculateRateSensitivity() {
      if (isOvernightTerm) {
        return calculateOvernightTermRateSensitivity();
      }
      return calculateOvernightRateSensitivity();
    }

    /** Calculate rate sensitivity for overnight term indices based on scenario. */
    private PointSensitivityBuilder calculateOvernightTermRateSensitivity() {
      LocalDate valuationDate = rates.getValuationDate();

      OvernightTermRateScenario scenario =
          determineOvernightTermScenario(valuationDate, termFirstFixingDate);

      return switch (scenario) {
        case FORWARD_LOOKING, FIXING_DATE_NO_FIXING -> calculateOvernightRateSensitivity();
        case HISTORICAL_FIXING, FIXING_DATE_WITH_FIXING -> PointSensitivityBuilder.none();
      };
    }

    private PointSensitivityBuilder calculateOvernightRateSensitivity() {
      double factor =
          this.pastCompositionFactor()
              * this.valuationCompositionFactor()
              / this.accrualFactorTotal;
      ObjDoublePair<PointSensitivityBuilder> compositionFactorAndSensitivityNonCutoff =
          this.compositionFactorAndSensitivityNonCutoff();
      ObjDoublePair<PointSensitivityBuilder> compositionFactorAndSensitivityCutoff =
          this.compositionFactorAndSensitivityCutoff();
      PointSensitivityBuilder combinedPointSensitivity =
          compositionFactorAndSensitivityNonCutoff
              .getFirst()
              .multipliedBy(compositionFactorAndSensitivityCutoff.getSecond() * factor);
      combinedPointSensitivity =
          combinedPointSensitivity.combinedWith(
              compositionFactorAndSensitivityCutoff
                  .getFirst()
                  .multipliedBy(compositionFactorAndSensitivityNonCutoff.getSecond() * factor));
      return combinedPointSensitivity;
    }

    private ObjDoublePair<PointSensitivityBuilder> compositionFactorAndSensitivityCutoff() {
      OvernightIndexObservation obs = this.computation.observeOn(this.lastFixingNonCutoff);
      if (this.nextFixing.isAfter(this.lastFixingNonCutoff)) {
        return ObjDoublePair.of(PointSensitivityBuilder.none(), 1.0);
      } else {
        double rate = this.rates.rate(obs);
        double compositionFactor = 1.0;
        double compositionFactorDerivative = 0.0;

        for (int i = 0; i < this.cutoffOffset - 1; ++i) {
          compositionFactor *= 1.0 + this.accrualFactorCutoff[i] * rate;
          compositionFactorDerivative +=
              this.accrualFactorCutoff[i] / (1.0 + this.accrualFactorCutoff[i] * rate);
        }

        compositionFactorDerivative *= compositionFactor;
        PointSensitivityBuilder rateSensitivity =
            this.cutoffOffset <= 1
                ? PointSensitivityBuilder.none()
                : this.rates.ratePointSensitivity(obs);
        rateSensitivity = rateSensitivity.multipliedBy(compositionFactorDerivative);
        return ObjDoublePair.of(rateSensitivity, compositionFactor);
      }
    }

    private ObjDoublePair<PointSensitivityBuilder> compositionFactorAndSensitivityNonCutoff() {
      if (!this.nextFixing.isAfter(this.lastFixingNonCutoff)) {
        OvernightIndexObservation obs = this.computation.observeOn(this.nextFixing);
        LocalDate startDate = obs.getEffectiveDate();
        LocalDate endDate = this.computation.calculateMaturityFromFixing(this.lastFixingNonCutoff);
        double accrualFactor = this.dayCount.yearFraction(startDate, endDate);
        double rate = this.rates.periodRate(obs, endDate);
        PointSensitivityBuilder rateSensitivity =
            this.rates.periodRatePointSensitivity(obs, endDate);
        rateSensitivity = rateSensitivity.multipliedBy(accrualFactor);
        return ObjDoublePair.of(rateSensitivity, 1.0 + accrualFactor * rate);
      } else {
        return ObjDoublePair.of(PointSensitivityBuilder.none(), 1.0);
      }
    }
  }
}
