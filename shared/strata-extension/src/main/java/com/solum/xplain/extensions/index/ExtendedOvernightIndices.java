package com.solum.xplain.extensions.index;

import static com.opengamma.strata.basics.date.BusinessDayConventions.MODIFIED_FOLLOWING;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.date.BusinessDayAdjustment;
import com.opengamma.strata.basics.date.DayCounts;
import com.opengamma.strata.basics.date.DaysAdjustment;
import com.opengamma.strata.basics.date.HolidayCalendarIds;
import com.opengamma.strata.basics.date.Tenor;
import com.opengamma.strata.basics.date.TenorAdjustment;
import org.jspecify.annotations.NullMarked;

import java.time.Period;

@NullMarked
public class ExtendedOvernightIndices {

  public static final OvernightTermIndex USD_SOFR_3M =
      new OvernightTermIndex(
          "USD-SOFR-3M",
          Currency.USD,
          true,
          HolidayCalendarIds.USGS,
          DaysAdjustment.ofBusinessDays(0, HolidayCalendarIds.USGS),
          DaysAdjustment.ofBusinessDays(0, HolidayCalendarIds.USGS),
          TenorAdjustment.ofLastBusinessDay(
              Tenor.of(Period.ofMonths(3)),
              BusinessDayAdjustment.of(MODIFIED_FOLLOWING, HolidayCalendarIds.USGS)),
          DayCounts.ACT_360,
          Tenor.of(Period.ofMonths(3)));
  public static final OvernightTermIndex USD_SOFR_6M =
      new OvernightTermIndex(
          "USD-SOFR-6M",
          Currency.USD,
          true,
          HolidayCalendarIds.USGS,
          DaysAdjustment.ofBusinessDays(0, HolidayCalendarIds.USGS),
          DaysAdjustment.ofBusinessDays(0, HolidayCalendarIds.USGS),
          TenorAdjustment.ofLastBusinessDay(
              Tenor.of(Period.ofMonths(6)),
              BusinessDayAdjustment.of(MODIFIED_FOLLOWING, HolidayCalendarIds.USGS)),
          DayCounts.ACT_360,
          Tenor.of(Period.ofMonths(6)));
}
